package impl

import (
	"time"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

const (
	// ConfigKeyPrefix 参数键名前缀
	ConfigKeyPrefix = "sys_config:"
)

// SysConfigServiceImpl 系统配置服务实现
type SysConfigServiceImpl struct {
	logger       *zap.Logger
	configRepo   repository.ConfigRepository
	redisService redis.RedisService
}

// NewSysConfigServiceImpl 创建系统配置服务实现
func NewSysConfigServiceImpl(logger *zap.Logger, configRepo repository.ConfigRepository, redisService redis.RedisService) service.ISysConfigService {
	return &SysConfigServiceImpl{
		logger:       logger,
		configRepo:   configRepo,
		redisService: redisService,
	}
}

// Init 初始化方法，加载参数缓存数据
func (s *SysConfigServiceImpl) Init() {
	s.LoadingConfigCache()
}

// SelectConfigById 查询参数配置信息
func (s *SysConfigServiceImpl) SelectConfigById(configId int64) domain.SysConfig {
	config, err := s.configRepo.SelectConfigById(configId)
	if err != nil {
		s.logger.Error("查询参数配置信息失败", zap.Int64("configId", configId), zap.Error(err))
		return domain.SysConfig{}
	}
	if config == nil {
		return domain.SysConfig{}
	}
	return *config
}

// SelectConfigByKey 根据键名查询参数配置信息
func (s *SysConfigServiceImpl) SelectConfigByKey(configKey string) domain.SysConfig {
	// 先从缓存中获取
	configValue, err := s.redisService.Get(s.GetCacheKey(configKey))
	if err == nil && configValue != "" {
		config := domain.SysConfig{
			ConfigKey:   configKey,
			ConfigValue: configValue,
		}
		return config
	}

	// 缓存中没有，从数据库获取
	config := &domain.SysConfig{
		ConfigKey: configKey,
	}
	result, err := s.configRepo.SelectConfig(config)
	if err != nil || result == nil {
		s.logger.Error("根据键名查询参数配置信息失败", zap.String("configKey", configKey), zap.Error(err))
		return domain.SysConfig{}
	}

	// 放入缓存
	s.redisService.Set(s.GetCacheKey(configKey), result.ConfigValue, 24*time.Hour)

	return *result
}

// SelectCaptchaEnabled 获取验证码开关
func (s *SysConfigServiceImpl) SelectCaptchaEnabled() bool {
	config := s.SelectConfigByKey("sys.account.captchaEnabled")
	return config.ConfigValue == "true"
}

// SelectConfigList 查询参数配置列表
func (s *SysConfigServiceImpl) SelectConfigList(config domain.SysConfig) []domain.SysConfig {
	configPtr := &domain.SysConfig{
		ConfigName: config.ConfigName,
		ConfigKey:  config.ConfigKey,
		ConfigType: config.ConfigType,
	}
	configPtr.SetParams(config.GetParams())

	configList, err := s.configRepo.SelectConfigList(configPtr)
	if err != nil {
		s.logger.Error("查询参数配置列表失败", zap.Error(err))
		return []domain.SysConfig{}
	}
	return configList
}

// InsertConfig 新增参数配置
func (s *SysConfigServiceImpl) InsertConfig(config domain.SysConfig) int {
	configPtr := &domain.SysConfig{
		ConfigName:  config.ConfigName,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
	}

	// 设置BaseEntity字段
	configPtr.CreateBy = config.CreateBy
	configPtr.CreateTime = config.CreateTime
	configPtr.UpdateBy = config.UpdateBy
	configPtr.UpdateTime = config.UpdateTime
	configPtr.Remark = config.Remark

	err := s.configRepo.InsertConfig(configPtr)
	if err != nil {
		s.logger.Error("新增参数配置失败", zap.Error(err))
		return 0
	}

	// 如果是系统内置参数，则刷新缓存
	if config.ConfigType == "Y" {
		s.redisService.Set(s.GetCacheKey(config.ConfigKey), config.ConfigValue, 24*time.Hour)
	}
	return 1
}

// UpdateConfig 修改参数配置
func (s *SysConfigServiceImpl) UpdateConfig(config domain.SysConfig) int {
	configPtr := &domain.SysConfig{
		ConfigId:    config.ConfigId,
		ConfigName:  config.ConfigName,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
	}

	// 设置BaseEntity字段
	configPtr.UpdateBy = config.UpdateBy
	configPtr.UpdateTime = config.UpdateTime
	configPtr.Remark = config.Remark

	err := s.configRepo.UpdateConfig(configPtr)
	if err != nil {
		s.logger.Error("修改参数配置失败", zap.Error(err))
		return 0
	}

	// 如果是系统内置参数，则刷新缓存
	if config.ConfigType == "Y" {
		s.redisService.Set(s.GetCacheKey(config.ConfigKey), config.ConfigValue, 24*time.Hour)
	}
	return 1
}

// DeleteConfigByIds 批量删除参数信息
func (s *SysConfigServiceImpl) DeleteConfigByIds(configIds []int64) {
	// 先查询所有配置，用于后续删除缓存
	var configs []domain.SysConfig
	for _, configId := range configIds {
		config, err := s.configRepo.SelectConfigById(configId)
		if err == nil && config != nil {
			configs = append(configs, *config)
		}
	}

	// 删除数据库中的配置
	err := s.configRepo.DeleteConfigByIds(configIds)
	if err != nil {
		s.logger.Error("批量删除参数信息失败", zap.Error(err))
		return
	}

	// 删除缓存
	for _, config := range configs {
		if config.ConfigType == "Y" {
			s.redisService.Del(s.GetCacheKey(config.ConfigKey))
		}
	}
}

// LoadingConfigCache 加载参数缓存数据
func (s *SysConfigServiceImpl) LoadingConfigCache() {
	config := &domain.SysConfig{
		ConfigType: "Y",
	}
	configList, err := s.configRepo.SelectConfigList(config)
	if err != nil {
		s.logger.Error("加载参数缓存数据失败", zap.Error(err))
		return
	}

	for _, config := range configList {
		s.redisService.Set(s.GetCacheKey(config.ConfigKey), config.ConfigValue, 24*time.Hour)
	}
}

// ClearConfigCache 清空参数缓存数据
func (s *SysConfigServiceImpl) ClearConfigCache() {
	config := &domain.SysConfig{
		ConfigType: "Y",
	}
	configList, err := s.configRepo.SelectConfigList(config)
	if err != nil {
		s.logger.Error("清空参数缓存数据失败", zap.Error(err))
		return
	}

	for _, config := range configList {
		s.redisService.Del(s.GetCacheKey(config.ConfigKey))
	}
}

// ResetConfigCache 重置参数缓存数据
func (s *SysConfigServiceImpl) ResetConfigCache() {
	s.ClearConfigCache()
	s.LoadingConfigCache()
}

// CheckConfigKeyUnique 校验参数键名是否唯一
func (s *SysConfigServiceImpl) CheckConfigKeyUnique(config domain.SysConfig) bool {
	existConfig, err := s.configRepo.CheckConfigKeyUnique(config.ConfigKey)
	if err != nil {
		s.logger.Error("校验参数键名是否唯一失败", zap.Error(err))
		return false
	}

	if existConfig != nil && existConfig.ConfigId != config.ConfigId {
		return false
	}
	return true
}

// GetCacheKey 获取缓存键值
func (s *SysConfigServiceImpl) GetCacheKey(configKey string) string {
	return ConfigKeyPrefix + configKey
}
