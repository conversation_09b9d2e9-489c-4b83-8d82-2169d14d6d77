package impl

import (
	"errors"
	"fmt"
	"time"

	"math/rand"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/utils"
	"go.uber.org/zap"
)

const (
	// CaptchaExpiration 验证码有效期（分钟）
	CaptchaExpiration = 2

	// CaptchaPrefix 验证码缓存key前缀
	CaptchaPrefix = "captcha:"
)

// SysLoginServiceImpl 登录服务实现
type SysLoginServiceImpl struct {
	logger          *zap.Logger
	tokenService    service.TokenService
	userRepo        repository.UserRepository
	configService   service.ISysConfigService
	passwordService service.SysPasswordService
	redisService    redis.RedisService
	menuService     service.ISysMenuService
}

// NewSysLoginServiceImpl 创建登录服务实现
func NewSysLoginServiceImpl(
	logger *zap.Logger,
	tokenService service.TokenService,
	userRepo repository.UserRepository,
	configService service.ISysConfigService,
	passwordService service.SysPasswordService,
	redisService redis.RedisService,
	menuService service.ISysMenuService,
) service.SysLoginService {
	return &SysLoginServiceImpl{
		logger:          logger,
		tokenService:    tokenService,
		userRepo:        userRepo,
		configService:   configService,
		passwordService: passwordService,
		redisService:    redisService,
		menuService:     menuService,
	}
}

// Login 登录验证
func (s *SysLoginServiceImpl) Login(username string, password string, code string, uuid string) (string, error) {
	// 验证码校验
	if err := s.ValidateCaptcha(username, code, uuid); err != nil {
		return "", err
	}

	// 登录前置校验
	if err := s.LoginPreCheck(username, password); err != nil {
		return "", err
	}

	// 用户验证
	user, err := s.userRepo.SelectUserByUserName(username)
	if err != nil {
		s.logger.Error("登录用户：用户不存在", zap.String("username", username), zap.Error(err))
		return "", errors.New("登录用户：用户不存在")
	}

	if user == nil {
		s.logger.Error("登录用户：用户不存在", zap.String("username", username))
		return "", errors.New("登录用户：用户不存在")
	}

	if user.DelFlag == "2" {
		s.logger.Error("登录用户：用户已被删除", zap.String("username", username))
		return "", errors.New("对不起，您的账号已被删除")
	}

	if user.Status == "1" {
		s.logger.Error("登录用户：用户已停用", zap.String("username", username))
		return "", errors.New("对不起，您的账号已停用")
	}

	// 密码校验
	if !s.passwordService.MatchPassword(password, user.Password) {
		s.logger.Error("登录用户：密码错误", zap.String("username", username))
		return "", errors.New("用户不存在/密码错误")
	}

	// 创建登录用户
	loginUser := &model.LoginUser{
		UserId:     user.UserId,
		DeptId:     user.DeptId,
		User:       *user,
		LoginTime:  time.Now().Unix(),
		ExpireTime: time.Now().Add(24 * time.Hour).Unix(),
	}

	// 生成token
	token := s.tokenService.CreateToken(loginUser)

	return token, nil
}

// ValidateCaptcha 验证验证码
func (s *SysLoginServiceImpl) ValidateCaptcha(username string, code string, uuid string) error {
	// 验证码开关
	captchaEnabled := s.configService.SelectCaptchaEnabled()
	if !captchaEnabled {
		return nil
	}

	if code == "" || uuid == "" {
		return errors.New("验证码不能为空")
	}

	// 获取验证码
	captchaKey := fmt.Sprintf("%s%s", CaptchaPrefix, uuid)
	captcha, err := s.redisService.Get(captchaKey)
	if err != nil {
		return errors.New("验证码已失效")
	}

	// 删除验证码
	s.redisService.Del(captchaKey)

	if captcha != code {
		return errors.New("验证码错误")
	}

	return nil
}

// LoginPreCheck 登录前置校验
func (s *SysLoginServiceImpl) LoginPreCheck(username string, password string) error {
	// 用户名或密码为空 错误
	if username == "" || password == "" {
		s.logger.Error("登录用户：用户名或密码为空")
		return errors.New("用户名或密码为空")
	}

	// 密码如果不在指定范围内 错误
	if len(password) < 5 || len(password) > 20 {
		s.logger.Error("登录用户：用户密码长度不在指定范围内", zap.String("username", username))
		return errors.New("用户密码长度不在指定范围内")
	}

	// 用户名不在指定范围内 错误
	if len(username) < 2 || len(username) > 20 {
		s.logger.Error("登录用户：用户名长度不在指定范围内", zap.String("username", username))
		return errors.New("用户名长度不在指定范围内")
	}

	return nil
}

// RecordLoginInfo 记录登录信息
func (s *SysLoginServiceImpl) RecordLoginInfo(userId int64) error {
	// 更新用户登录信息
	loginTime := time.Now()
	user := &domain.SysUser{
		UserId:    userId,
		LoginIp:   "127.0.0.1", // 暂时使用默认IP，后续可以从请求上下文中获取
		LoginDate: &loginTime,
	}

	err := s.userRepo.UpdateUser(user)
	if err != nil {
		s.logger.Error("记录登录信息失败", zap.Int64("userId", userId), zap.Error(err))
		return err
	}

	return nil
}

// Logout 退出登录
func (s *SysLoginServiceImpl) Logout(token string) error {
	s.tokenService.DelLoginUser(token)
	return nil
}

// GetUserInfo 获取用户信息
func (s *SysLoginServiceImpl) GetUserInfo(userId int64) (map[string]interface{}, error) {
	user, err := s.userRepo.SelectUserById(userId)
	if err != nil {
		s.logger.Error("获取用户信息失败", zap.Int64("userId", userId), zap.Error(err))
		return nil, err
	}

	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 角色集合
	// 暂时使用空字符串数组，后续实现角色查询功能
	roles := []string{}

	// 权限集合
	// 暂时使用空字符串数组，后续实现权限查询功能
	permissions := []string{}

	result := map[string]interface{}{
		"user":        user,
		"roles":       roles,
		"permissions": permissions,
	}

	return result, nil
}

// GetRouters 获取路由信息
func (s *SysLoginServiceImpl) GetRouters(userId int64) ([]model.Router, error) {
	// 暂时返回空路由数组，后续实现菜单树和路由构建功能
	return []model.Router{}, nil
}

// GenerateCaptcha 生成验证码
func (s *SysLoginServiceImpl) GenerateCaptcha() (string, string, error) {
	// 生成验证码
	code := generateCaptchaCode(4)
	uuid := utils.GenerateUUID()

	// 保存验证码
	captchaKey := fmt.Sprintf("%s%s", CaptchaPrefix, uuid)
	err := s.redisService.Set(captchaKey, code, CaptchaExpiration*time.Minute)
	if err != nil {
		s.logger.Error("保存验证码失败", zap.Error(err))
		return "", "", err
	}

	return uuid, code, nil
}

// generateCaptchaCode 生成指定长度的验证码
func generateCaptchaCode(length int) string {
	const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}
