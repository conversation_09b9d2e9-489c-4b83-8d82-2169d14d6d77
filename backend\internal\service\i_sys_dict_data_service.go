package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysDictDataService 字典数据服务接口
type ISysDictDataService interface {
	// SelectDictDataList 根据条件分页查询字典数据
	SelectDictDataList(dictData domain.SysDictData) []domain.SysDictData

	// SelectDictLabel 根据字典类型和字典键值查询字典数据信息
	SelectDictLabel(dictType string, dictValue string) string

	// SelectDictDataById 根据字典数据ID查询信息
	SelectDictDataById(dictCode int64) domain.SysDictData

	// DeleteDictDataByIds 批量删除字典数据信息
	DeleteDictDataByIds(dictCodes []int64)

	// InsertDictData 新增保存字典数据信息
	InsertDictData(dictData domain.SysDictData) int

	// UpdateDictData 修改保存字典数据信息
	UpdateDictData(dictData domain.SysDictData) int
}
