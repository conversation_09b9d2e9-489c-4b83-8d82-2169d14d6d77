package impl

import (
	"fmt"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/utils"
	"go.uber.org/zap"
)

const (
	// TokenPrefix Token前缀
	TokenPrefix = "Bearer "

	// TokenHeader Token请求头
	TokenHeader = "Authorization"

	// TokenKey Token缓存key前缀
	TokenKey = "login_tokens:"
)

// TokenServiceImpl Token服务实现
type TokenServiceImpl struct {
	logger       *zap.Logger
	jwtConfig    *config.JWTConfig
	redisService redis.RedisService
}

// NewTokenServiceImpl 创建Token服务实现
func NewTokenServiceImpl(logger *zap.Logger, jwtConfig *config.JWTConfig, redisService redis.RedisService) service.TokenService {
	return &TokenServiceImpl{
		logger:       logger,
		jwtConfig:    jwtConfig,
		redisService: redisService,
	}
}

// GetLoginUser 获取登录用户
func (s *TokenServiceImpl) GetLoginUser(ctx *gin.Context) *model.LoginUser {
	token := s.GetToken(ctx)
	if token == "" {
		return nil
	}

	claims := s.ParseToken(token)
	if claims == nil {
		return nil
	}

	uuid := claims.GetUUID()
	tokenKey := s.GetTokenKey(uuid)
	userKey, err := s.redisService.Get(tokenKey)
	if err != nil || userKey == "" {
		return nil
	}

	// TODO: 从Redis中获取用户信息并反序列化为LoginUser对象
	// 暂时返回nil，后续实现
	return nil
}

// SetLoginUser 设置登录用户
func (s *TokenServiceImpl) SetLoginUser(loginUser *model.LoginUser) {
	if loginUser != nil && loginUser.GetToken() != "" {
		tokenKey := s.GetTokenKey(loginUser.GetToken())
		expireTime := s.jwtConfig.GetExpireTime()

		// TODO: 将LoginUser对象序列化为JSON字符串并存入Redis
		// 暂时简单存储用户ID
		userIdStr := fmt.Sprintf("%d", loginUser.GetUserId())
		s.redisService.Set(tokenKey, userIdStr, expireTime)
	}
}

// DelLoginUser 删除登录用户
func (s *TokenServiceImpl) DelLoginUser(token string) {
	if token != "" {
		claims := s.ParseToken(token)
		if claims != nil {
			uuid := claims.GetUUID()
			tokenKey := s.GetTokenKey(uuid)
			s.redisService.Del(tokenKey)
		}
	}
}

// CreateToken 创建Token
func (s *TokenServiceImpl) CreateToken(loginUser *model.LoginUser) string {
	uuid := utils.GenerateUUID()
	loginUser.SetToken(uuid)

	s.RefreshToken(loginUser)

	claims := map[string]interface{}{
		"userId":         loginUser.GetUserId(),
		"username":       loginUser.GetUser().UserName,
		"uuid":           uuid,
		"loginTime":      loginUser.GetLoginTime(),
		"expirationTime": loginUser.GetExpireTime(),
	}

	return s.CreateJWTToken(claims)
}

// VerifyToken 验证Token有效期，相差不足20分钟自动刷新缓存
func (s *TokenServiceImpl) VerifyToken(loginUser *model.LoginUser) {
	expireTime := loginUser.GetExpireTime()
	currentTime := time.Now().Unix()
	// 相差不足20分钟，自动刷新缓存
	if expireTime-currentTime <= 20*60 {
		s.RefreshToken(loginUser)
	}
}

// RefreshToken 刷新Token有效期
func (s *TokenServiceImpl) RefreshToken(loginUser *model.LoginUser) {
	loginUser.SetLoginTime(time.Now().Unix())
	expireTime := time.Now().Add(s.jwtConfig.GetExpireTime()).Unix()
	loginUser.SetExpireTime(expireTime)

	// 缓存用户信息
	tokenKey := s.GetTokenKey(loginUser.GetToken())
	expiration := s.jwtConfig.GetExpireTime()

	// TODO: 将LoginUser对象序列化为JSON字符串并存入Redis
	// 暂时简单存储用户ID
	userIdStr := fmt.Sprintf("%d", loginUser.GetUserId())
	s.redisService.Set(tokenKey, userIdStr, expiration)
}

// SetUserAgent 设置用户代理信息
func (s *TokenServiceImpl) SetUserAgent(loginUser *model.LoginUser) {
	// TODO: 从请求上下文中获取用户代理信息
	// 暂时不实现
}

// CreateJWTToken 创建JWT Token
func (s *TokenServiceImpl) CreateJWTToken(claims map[string]interface{}) string {
	token := jwt.New(jwt.GetSigningMethod(s.jwtConfig.GetSigningMethod()))
	token.Claims = jwt.MapClaims(claims)
	tokenString, err := token.SignedString([]byte(s.jwtConfig.Secret))
	if err != nil {
		s.logger.Error("生成Token失败", zap.Error(err))
		return ""
	}
	return tokenString
}

// ParseToken 解析Token
func (s *TokenServiceImpl) ParseToken(token string) *model.Claims {
	token = strings.TrimPrefix(token, TokenPrefix)
	tokenClaims, err := jwt.ParseWithClaims(token, &model.Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.jwtConfig.Secret), nil
	})

	if err != nil {
		s.logger.Error("解析Token失败", zap.Error(err))
		return nil
	}

	if claims, ok := tokenClaims.Claims.(*model.Claims); ok && tokenClaims.Valid {
		return claims
	}

	return nil
}

// GetUsernameFromToken 从Token中获取用户名
func (s *TokenServiceImpl) GetUsernameFromToken(token string) string {
	claims := s.ParseToken(token)
	if claims != nil {
		return claims.GetUsername()
	}
	return ""
}

// GetToken 获取请求Token
func (s *TokenServiceImpl) GetToken(ctx *gin.Context) string {
	token := ctx.GetHeader(TokenHeader)
	if token != "" && strings.HasPrefix(token, TokenPrefix) {
		return token
	}
	return ""
}

// GetTokenKey 获取Token缓存key
func (s *TokenServiceImpl) GetTokenKey(uuid string) string {
	return fmt.Sprintf("%s%s", TokenKey, uuid)
}
